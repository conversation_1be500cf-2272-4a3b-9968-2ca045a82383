"use client";

import {useState} from "react";
import {enableStaticRendering, observer} from "mobx-react-lite";
import {CreateBillModel, InputType} from "@/app/bills/new/model";
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow} from "@/components/ui/table";
import {toJS} from "mobx";
import {Input} from "@/components/ui/input";
import {navigateController} from "@/app/bills/new/lib";
import {Button} from "@/components/ui/button";
import {Card, CardContent, CardHeader, CardTitle} from "@/components/ui/card";
import {Navbar} from "@/components/layout/navbar";
import {truncateNumber} from "@/lib/utils";
import {MinusCircle, Receipt, Plus, Trash2, RotateCcw} from "lucide-react";
import {UserSelectLookup} from "@/components/ui/user-select-lookup";
import {Toolt<PERSON>, TooltipUI} from "@/components/ui/tooltip";

enableStaticRendering(typeof window === 'undefined')


const AddBillPage = observer(() => {
    const [createBillModel] = useState(() => new CreateBillModel())

    console.log(toJS(createBillModel.rows))
    console.log(createBillModel.toJSON())

    const renderByInputType = (inputType: keyof typeof InputType, val: string, rowIdx: number, colIdx: number) => {
        const totalSumCol = rowIdx !== 0 && colIdx === createBillModel.tableBody[0].length - 1
        const feeCol = rowIdx !== 0 && colIdx === createBillModel.tableBody[0].length - 2

        switch (inputType) {
            case InputType.ITEM_TITLE:
                return (
                    <Input
                        id={`input-${rowIdx}-${colIdx}`}
                        placeholder={'Введите название'}
                        value={val}
                        onChange={(e) => {
                            createBillModel.setCell(rowIdx, colIdx, e.target.value);
                        }}
                        onKeyDown={navigateController}
                        className="border-0 bg-white/70 backdrop-blur-sm shadow-sm focus:shadow-md transition-all focus:ring-2 focus:ring-blue-500/20"
                    />
                )
            case InputType.PRICE:
                return (
                    <Input
                        id={`input-${rowIdx}-${colIdx}`}
                        placeholder={'Введите цену'}
                        value={val}
                        onChange={(e) => {
                            createBillModel.setCell(rowIdx, colIdx, e.target.value);
                        }}
                        onKeyDown={navigateController}
                        className="border-0 bg-white/70 backdrop-blur-sm shadow-sm focus:shadow-md transition-all focus:ring-2 focus:ring-blue-500/20"
                    />
                )
            case InputType.USER_SELECT:
                return (
                    <UserSelectLookup
                        id={`input-${rowIdx}-${colIdx}`}
                        placeholder={'Введите имя друга'}
                        value={val}
                        onChange={(value) => {
                            createBillModel.setCell(rowIdx, colIdx, value);
                        }}
                        onKeyDown={navigateController}
                        className="border-0 bg-white/70 backdrop-blur-sm shadow-sm focus:shadow-md transition-all focus:ring-2 focus:ring-blue-500/20"
                    />
                )
            case InputType.TEXT:
                if (feeCol) {
                    return (
                        <div className="text-center font-medium text-orange-600 bg-orange-50 px-2 py-1 rounded">
                            {truncateNumber(createBillModel.serviceFeeSum[rowIdx])}
                        </div>
                    )
                }
                if (totalSumCol) {
                    return (
                        <div className="text-center font-bold text-blue-600 bg-blue-50 px-2 py-1 rounded">
                            {truncateNumber(createBillModel.totalRowsSum[rowIdx])}
                        </div>
                    )
                }
                return (
                    <div className="text-center text-gray-700">
                        {val}
                    </div>
                )
            default:
                return null

        }
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100">
            <Navbar/>

            <div className="container mx-auto px-4 py-8">
                {/* Header */}
                <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-10 gap-4">
                    <div>
                        <h1 className="text-4xl font-bold text-gray-900 mb-2">
                            Создать новый счет
                        </h1>
                        <p className="text-lg text-gray-600">
                            Добавьте позиции и участников для разделения счета
                        </p>
                    </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-3 mb-6">
                    <Button
                        onClick={createBillModel.addRow}
                        variant="outline"
                        className="bg-white/80 backdrop-blur-sm border-0 shadow-md hover:shadow-lg transition-all"
                    >
                        <Plus className="h-4 w-4 mr-2"/>
                        Добавить строку
                    </Button>
                    <Button
                        onClick={createBillModel.addCol}
                        variant="outline"
                        className="bg-white/80 backdrop-blur-sm border-0 shadow-md hover:shadow-lg transition-all"
                    >
                        <Plus className="h-4 w-4 mr-2"/>
                        Добавить колонку
                    </Button>
                    <Button
                        variant="outline"
                        onClick={createBillModel.clearAll}
                        className="bg-white/80 backdrop-blur-sm border-0 shadow-md hover:shadow-lg transition-all text-orange-600 hover:text-orange-700"
                    >
                        <RotateCcw className="h-4 w-4 mr-2"/>
                        Очистить все
                    </Button>
                    <Button
                        variant="outline"
                        onClick={createBillModel.reset}
                        className="bg-white/80 backdrop-blur-sm border-0 shadow-md hover:shadow-lg transition-all text-red-600 hover:text-red-700"
                    >
                        <Trash2 className="h-4 w-4 mr-2"/>
                        Удалить все
                    </Button>
                </div>

                {/* Table Card */}
                <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm">
                    <CardHeader className="pb-4">
                        <CardTitle className="flex justify-between">
                            <div className="flex items-center text-xl">
                                <Receipt className="h-6 w-6 mr-3 text-blue-600"/>
                                Детали счета
                            </div>
                            <Button
                                onClick={() => console.log('DATA: ', createBillModel.toJSON())}
                                className="bg-blue-600 hover:bg-blue-700 border-0 shadow-lg px-8 py-3 text-base"
                            >
                                <Receipt className="h-5 w-5 mr-2"/>
                                Создать счет
                            </Button>
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-2">
                        <div className="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow className="border-b-2">
                                        <TableHead className="w-12"></TableHead>
                                        {
                                            createBillModel.tableHeader.map((header, idx) => {
                                                return (
                                                    <TableHead key={header.key} className="text-center">
                                                        {
                                                            (idx === 0 || idx >= createBillModel.tableHeader.length - 2) || createBillModel.tableHeader.length < 5 ? null :
                                                                <TooltipUI content="Удалить колонку">
                                                                    <Button
                                                                        variant="ghost"
                                                                        size="sm"
                                                                        onClick={() => createBillModel.removeCol(idx)}
                                                                        className="cursor-pointer h-6 w-6 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                                                                    >
                                                                        <MinusCircle className="h-4 w-4"/>
                                                                    </Button>
                                                                </TooltipUI>
                                                        }
                                                    </TableHead>
                                                )
                                            })
                                        }
                                    </TableRow>
                                    <TableRow className="bg-gray-50/50">
                                        <TableHead className="w-12"></TableHead>
                                        {
                                            createBillModel.tableHeader.map((header, idx) => (
                                                <TableHead key={header.key} className="p-2">
                                                    {
                                                        renderByInputType(header.inputType, header.value, 0, idx)
                                                    }
                                                </TableHead>
                                            ))
                                        }
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {createBillModel.tableBody.map((row, rowIdx) => (
                                        <TableRow key={rowIdx} className="hover:bg-gray-50/30 transition-colors">
                                            <TableCell className="w-12 text-center">
                                                {
                                                    createBillModel.tableBody.length < 2 ? null :
                                                        <TooltipUI content="Удалить строку">
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                                onClick={() => createBillModel.removeRow(rowIdx)}
                                                                className="cursor-pointer h-6 w-6 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                                                            >
                                                                <MinusCircle className="h-4 w-4"/>
                                                            </Button>
                                                        </TooltipUI>
                                                }
                                            </TableCell>
                                            {
                                                row.map((col, colIdx) => (
                                                    <TableCell key={col.key} className="p-2">
                                                        {renderByInputType(col.inputType, col.value, rowIdx + 1, colIdx)}
                                                    </TableCell>
                                                ))
                                            }
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    )
})

AddBillPage.displayName = "AddBillPage"

export default AddBillPage


